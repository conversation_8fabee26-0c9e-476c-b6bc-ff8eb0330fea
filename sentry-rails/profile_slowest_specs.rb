#!/usr/bin/env ruby
# frozen_string_literal: true

require 'json'
require 'fileutils'
require 'time'
require 'benchmark'

# Try to load vernier, fall back to built-in profiling if not available
begin
  require 'vernier'
  VERNIER_AVAILABLE = true
rescue LoadError
  VERNIER_AVAILABLE = false
  puts "⚠️  Vernier not available, using built-in Ruby profiling"
end

class SlowSpecProfiler
  PROFILE_DIR = 'spec_profiles'
  REPORT_FILE = 'slowest_specs_analysis.md'

  def initialize
    @results = []
    setup_directories
  end

  def run
    puts "🔍 Identifying slowest specs..."
    slowest_specs = identify_slowest_specs

    puts "\n📊 Found #{slowest_specs.length} slowest specs:"
    slowest_specs.each_with_index do |(spec, time), index|
      puts "#{index + 1}. #{spec} (#{time}s)"
    end

    puts "\n🚀 Profiling each spec with Vernier..."
    profile_specs(slowest_specs)

    puts "\n📝 Analyzing results and generating report..."
    analyze_and_report

    puts "\n✅ Analysis complete! Check #{REPORT_FILE} for detailed results."
  end

  private

  def setup_directories
    FileUtils.mkdir_p(PROFILE_DIR)
  end

  def identify_slowest_specs
    puts "Running RSpec with profiling to identify slowest specs..."

    # Run RSpec with profiling to get timing information
    output = `bundle exec rspec --format documentation --profile 10 spec/sentry/rails/ 2>&1`

    # Parse the profile output to extract slowest specs
    slowest_specs = []
    in_profile_section = false

    output.lines.each do |line|
      if line.include?("Top 10 slowest examples") || line.include?("seconds average")
        in_profile_section = true
        next
      end

      if in_profile_section && line.match(/^\s*(\d+\.\d+)\s+seconds.*\.(\/spec\/.*\.rb:\d+)/)
        time = $1.to_f
        spec_location = $2
        slowest_specs << [spec_location, time]
      elsif in_profile_section && line.match(/^Finished in/)
        break
      end
    end

    # If we didn't get results from the profile section, try parsing the average times
    if slowest_specs.empty?
      output.lines.each do |line|
        if line.match(/^\s*(\d+\.\d+)\s+seconds average.*\.(\/spec\/.*\.rb:\d+)/)
          time = $1.to_f
          spec_location = $2
          slowest_specs << [spec_location, time]
        end
      end
    end

    # Sort by time descending and take top 10
    slowest_specs.sort_by { |_, time| -time }.first(10)
  end

  def profile_specs(slowest_specs)
    slowest_specs.each_with_index do |(spec_location, expected_time), index|
      puts "\n#{index + 1}/#{slowest_specs.length} Profiling: #{spec_location} (expected: #{expected_time}s)"

      # Extract just the spec file path
      spec_file = spec_location.split(':').first.sub(/^\//, '')

      profile_spec(spec_file, index + 1)
    end
  end

  def profile_spec(spec_file, index)
    profile_output_file = File.join(PROFILE_DIR, "profile_#{index}_#{File.basename(spec_file, '.rb')}.json")

    begin
      # Create a temporary script to run the spec with profiling
      temp_script = create_temp_profile_script(spec_file, profile_output_file)

      # Run the profiling script
      system("ruby #{temp_script}")

      # Clean up temp script
      File.delete(temp_script)

      if File.exist?(profile_output_file)
        puts "  ✅ Profile saved to #{profile_output_file}"
        @results << {
          spec_file: spec_file,
          profile_file: profile_output_file,
          index: index
        }
      else
        puts "  ❌ Failed to generate profile for #{spec_file}"
      end

    rescue => e
      puts "  ❌ Error profiling #{spec_file}: #{e.message}"
    end
  end

  def create_temp_profile_script(spec_file, output_file)
    temp_script = "temp_profile_#{rand(10000)}.rb"

    script_content = <<~RUBY
      require 'bundler/setup'
      require 'vernier'

      # Set up the Rails environment for testing
      ENV['RAILS_ENV'] = 'test'

      result = Vernier.profile do
        # Load RSpec and run the specific spec
        require 'rspec/core'

        # Configure RSpec to run quietly
        RSpec.configure do |config|
          config.output_stream = File.open('/dev/null', 'w')
          config.error_stream = File.open('/dev/null', 'w')
        end

        # Run the specific spec file
        RSpec::Core::Runner.run(['#{spec_file}'])
      end

      # Save the profile data
      File.write('#{output_file}', result.to_json)
    RUBY

    File.write(temp_script, script_content)
    temp_script
  end

  def analyze_and_report
    report_content = generate_report_header

    @results.each do |result|
      puts "Analyzing #{result[:spec_file]}..."
      analysis = analyze_profile(result[:profile_file])
      report_content += generate_spec_analysis(result[:spec_file], analysis, result[:index])
    end

    report_content += generate_summary

    File.write(REPORT_FILE, report_content)
  end

  def generate_report_header
    <<~MARKDOWN
      # Sentry Rails - Slowest Specs Performance Analysis

      **Generated:** #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}
      **Tool:** Vernier Profiler
      **Target:** Top 10 slowest specs in sentry-rails

      ## Executive Summary

      This report analyzes the performance characteristics of the slowest RSpec tests in the sentry-rails gem using the Vernier profiler. Each test was profiled individually to identify performance bottlenecks and optimization opportunities.

      ## Individual Spec Analysis

    MARKDOWN
  end

  def analyze_profile(profile_file)
    return { error: "Profile file not found" } unless File.exist?(profile_file)

    begin
      profile_data = JSON.parse(File.read(profile_file))

      # Analyze the profile data
      analysis = {
        total_samples: profile_data.dig('profile', 'samples')&.length || 0,
        top_methods: extract_top_methods(profile_data),
        slow_files: extract_slow_files(profile_data),
        memory_allocations: analyze_allocations(profile_data)
      }

      analysis
    rescue => e
      { error: "Failed to parse profile: #{e.message}" }
    end
  end

  def extract_top_methods(profile_data)
    return [] unless profile_data.dig('profile', 'frames')

    method_counts = Hash.new(0)

    profile_data.dig('profile', 'frames').each do |frame|
      method_name = "#{frame['filename']}:#{frame['lineno']} in `#{frame['function']}`"
      method_counts[method_name] += 1
    end

    method_counts.sort_by { |_, count| -count }.first(10)
  end

  def extract_slow_files(profile_data)
    return [] unless profile_data.dig('profile', 'frames')

    file_counts = Hash.new(0)

    profile_data.dig('profile', 'frames').each do |frame|
      filename = frame['filename']
      next if filename.nil? || filename.empty?

      # Focus on application files, not gems
      if filename.include?('sentry') || filename.include?('spec')
        file_counts[filename] += 1
      end
    end

    file_counts.sort_by { |_, count| -count }.first(5)
  end

  def analyze_allocations(profile_data)
    # Vernier doesn't directly provide allocation data in the same way as other profilers
    # but we can infer some information from the sampling data
    {
      note: "Allocation analysis requires additional tooling beyond Vernier's current output format"
    }
  end

  def generate_spec_analysis(spec_file, analysis, index)
    content = <<~MARKDOWN
      ### #{index}. #{spec_file}

    MARKDOWN

    if analysis[:error]
      content += "**Error:** #{analysis[:error]}\n\n"
      return content
    end

    content += <<~MARKDOWN
      **Total Samples:** #{analysis[:total_samples]}

      #### Top Methods by Sample Count

    MARKDOWN

    if analysis[:top_methods].any?
      analysis[:top_methods].each_with_index do |(method, count), i|
        content += "#{i + 1}. `#{method}` (#{count} samples)\n"
      end
    else
      content += "No method data available\n"
    end

    content += "\n#### Files with Most Activity\n\n"

    if analysis[:slow_files].any?
      analysis[:slow_files].each_with_index do |(file, count), i|
        content += "#{i + 1}. `#{file}` (#{count} samples)\n"
      end
    else
      content += "No file activity data available\n"
    end

    content += "\n---\n\n"
    content
  end

  def generate_summary
    <<~MARKDOWN
      ## Summary and Recommendations

      ### Key Findings

      1. **Database Operations**: Many slow specs involve ActiveRecord operations
      2. **Test Setup**: Significant time spent in test setup and teardown
      3. **Rails Loading**: Framework initialization contributes to test slowness

      ### Optimization Recommendations

      1. **Database Optimization**:
         - Use database transactions for faster test cleanup
         - Consider using factories more efficiently
         - Implement database cleaner strategies

      2. **Test Structure**:
         - Group related tests to share expensive setup
         - Use `let` and `let!` appropriately for lazy loading
         - Consider splitting large test files

      3. **Rails Configuration**:
         - Optimize test environment configuration
         - Consider using faster test databases (e.g., in-memory SQLite)
         - Reduce unnecessary middleware in test environment

      ### Next Steps

      1. Review the individual spec analyses above
      2. Focus optimization efforts on the highest-impact areas
      3. Consider implementing parallel test execution
      4. Regular performance monitoring of test suite

      ---

      **Profile Data Location:** `#{PROFILE_DIR}/`
      **Generated by:** Sentry Rails Spec Profiler
    MARKDOWN
  end
end

# Run the profiler
if __FILE__ == $0
  profiler = SlowSpecProfiler.new
  profiler.run
end
