#!/usr/bin/env ruby
# frozen_string_literal: true

require 'bundler/setup'
require 'benchmark'
require 'json'
require 'fileutils'
require 'time'

class EnhancedSpecAnalyzer
  ANALYSIS_DIR = 'enhanced_analysis'
  REPORT_FILE = 'enhanced_slowest_specs_report.md'
  
  def initialize
    @results = []
    setup_directories
  end

  def run
    puts "🔍 Identifying slowest specs with detailed timing analysis..."
    slowest_specs = identify_slowest_specs
    
    puts "\n📊 Found #{slowest_specs.length} slowest specs:"
    slowest_specs.each_with_index do |(spec, time), index|
      puts "#{index + 1}. #{spec} (#{time}s)"
    end
    
    puts "\n🚀 Running detailed analysis on each spec..."
    analyze_specs(slowest_specs)
    
    puts "\n📝 Generating comprehensive report..."
    generate_report
    
    puts "\n✅ Enhanced analysis complete! Check #{REPORT_FILE} for detailed results."
  end

  private

  def setup_directories
    FileUtils.mkdir_p(ANALYSIS_DIR)
  end

  def identify_slowest_specs
    puts "Running RSpec with profiling to identify slowest specs..."
    
    # Run RSpec with profiling to get timing information
    output = `bundle exec rspec --format documentation --profile 10 spec/sentry/rails/ 2>&1`
    
    # Parse the profile output to extract slowest specs
    slowest_specs = []
    in_profile_section = false
    
    output.lines.each do |line|
      if line.include?("Top 10 slowest examples") || line.include?("seconds average")
        in_profile_section = true
        next
      end
      
      if in_profile_section && line.match(/^\s*(\d+\.\d+)\s+seconds.*\.(\/spec\/.*\.rb:\d+)/)
        time = $1.to_f
        spec_location = $2
        slowest_specs << [spec_location, time]
      elsif in_profile_section && line.match(/^Finished in/)
        break
      end
    end
    
    # If we didn't get results from the profile section, try parsing the average times
    if slowest_specs.empty?
      output.lines.each do |line|
        if line.match(/^\s*(\d+\.\d+)\s+seconds average.*\.(\/spec\/.*\.rb:\d+)/)
          time = $1.to_f
          spec_location = $2
          slowest_specs << [spec_location, time]
        end
      end
    end
    
    # Sort by time descending and take top 10
    slowest_specs.sort_by { |_, time| -time }.first(10)
  end

  def analyze_specs(slowest_specs)
    slowest_specs.each_with_index do |(spec_location, expected_time), index|
      puts "\n#{index + 1}/#{slowest_specs.length} Analyzing: #{spec_location} (expected: #{expected_time}s)"
      
      # Extract just the spec file path
      spec_file = spec_location.split(':').first.sub(/^\//, '')
      
      analyze_spec(spec_file, index + 1, expected_time)
    end
  end

  def analyze_spec(spec_file, index, expected_time)
    analysis_file = File.join(ANALYSIS_DIR, "analysis_#{index}_#{File.basename(spec_file, '.rb')}.json")
    
    begin
      # Run detailed timing analysis
      timing_data = run_timing_analysis(spec_file)
      
      # Analyze the spec file content
      content_analysis = analyze_spec_content(spec_file)
      
      # Combine all analysis data
      full_analysis = {
        spec_file: spec_file,
        expected_time: expected_time,
        timing: timing_data,
        content: content_analysis,
        timestamp: Time.now.iso8601
      }
      
      File.write(analysis_file, JSON.pretty_generate(full_analysis))
      
      puts "  ✅ Analysis saved to #{analysis_file}"
      @results << {
        spec_file: spec_file,
        analysis_file: analysis_file,
        index: index,
        data: full_analysis
      }
      
    rescue => e
      puts "  ❌ Error analyzing #{spec_file}: #{e.message}"
    end
  end

  def run_timing_analysis(spec_file)
    timing_data = {}
    
    # Run the spec multiple times to get consistent timing
    times = []
    3.times do |run|
      puts "    Run #{run + 1}/3..."
      time = Benchmark.realtime do
        system("bundle exec rspec #{spec_file} --format progress > /dev/null 2>&1")
      end
      times << time
    end
    
    timing_data[:runs] = times
    timing_data[:average] = times.sum / times.length
    timing_data[:min] = times.min
    timing_data[:max] = times.max
    timing_data[:variance] = calculate_variance(times)
    
    timing_data
  end

  def analyze_spec_content(spec_file)
    return { error: "File not found" } unless File.exist?(spec_file)
    
    content = File.read(spec_file)
    lines = content.lines
    
    analysis = {
      total_lines: lines.length,
      test_count: count_tests(content),
      setup_complexity: analyze_setup_complexity(content),
      database_operations: count_database_operations(content),
      external_calls: count_external_calls(content),
      factory_usage: count_factory_usage(content),
      let_statements: count_let_statements(content)
    }
    
    analysis
  end

  def count_tests(content)
    content.scan(/^\s*(it|specify|example)\s+/).length
  end

  def analyze_setup_complexity(content)
    setup_indicators = {
      before_blocks: content.scan(/^\s*before\s+/).length,
      let_blocks: content.scan(/^\s*let[!]?\s+/).length,
      subject_blocks: content.scan(/^\s*subject\s+/).length,
      context_blocks: content.scan(/^\s*context\s+/).length,
      describe_blocks: content.scan(/^\s*describe\s+/).length
    }
    
    # Calculate complexity score
    complexity_score = setup_indicators.values.sum
    
    setup_indicators.merge(complexity_score: complexity_score)
  end

  def count_database_operations(content)
    {
      create_calls: content.scan(/\.create[!\(]/).length,
      save_calls: content.scan(/\.save[!\(]/).length,
      find_calls: content.scan(/\.find[!\(]/).length,
      where_calls: content.scan(/\.where[!\(]/).length,
      update_calls: content.scan(/\.update[!\(]/).length,
      destroy_calls: content.scan(/\.destroy[!\(]/).length
    }
  end

  def count_external_calls(content)
    {
      http_requests: content.scan(/(get|post|put|patch|delete)\s+["']/).length,
      api_calls: content.scan(/\.(get|post|put|patch|delete)\(/).length,
      file_operations: content.scan(/(File\.|Dir\.)/).length,
      system_calls: content.scan(/system\(|`/).length
    }
  end

  def count_factory_usage(content)
    {
      create_factory: content.scan(/create\(/).length,
      build_factory: content.scan(/build\(/).length,
      attributes_for: content.scan(/attributes_for\(/).length
    }
  end

  def count_let_statements(content)
    {
      let_lazy: content.scan(/^\s*let\s+/).length,
      let_eager: content.scan(/^\s*let!\s+/).length
    }
  end

  def calculate_variance(numbers)
    mean = numbers.sum.to_f / numbers.length
    variance = numbers.sum { |n| (n - mean) ** 2 } / numbers.length
    variance.round(6)
  end

  def generate_report
    report_content = generate_report_header
    
    @results.each do |result|
      puts "Generating report section for #{result[:spec_file]}..."
      report_content += generate_spec_report_section(result)
    end
    
    report_content += generate_summary_and_recommendations
    
    File.write(REPORT_FILE, report_content)
  end

  def generate_report_header
    <<~MARKDOWN
      # Sentry Rails - Enhanced Slowest Specs Performance Analysis
      
      **Generated:** #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}
      **Tool:** Enhanced Spec Analyzer with Timing Analysis
      **Target:** Top 10 slowest specs in sentry-rails
      
      ## Executive Summary
      
      This report provides a comprehensive analysis of the slowest RSpec tests in the sentry-rails gem, including detailed timing analysis, content analysis, and performance recommendations.
      
      ## Individual Spec Analysis
      
    MARKDOWN
  end

  def generate_spec_report_section(result)
    data = result[:data]
    timing = data[:timing]
    content = data[:content]
    
    <<~MARKDOWN
      ### #{result[:index]}. #{data[:spec_file]}
      
      **Expected Time:** #{data[:expected_time]}s
      **Average Actual Time:** #{timing[:average]&.round(4)}s
      **Min Time:** #{timing[:min]&.round(4)}s
      **Max Time:** #{timing[:max]&.round(4)}s
      **Variance:** #{timing[:variance]}
      
      #### Content Analysis
      - **Total Lines:** #{content[:total_lines]}
      - **Test Count:** #{content[:test_count]}
      - **Setup Complexity Score:** #{content.dig(:setup_complexity, :complexity_score)}
      - **Before Blocks:** #{content.dig(:setup_complexity, :before_blocks)}
      - **Let Statements:** #{content.dig(:let_statements, :let_lazy)} lazy, #{content.dig(:let_statements, :let_eager)} eager
      
      #### Database Operations
      - **Create:** #{content.dig(:database_operations, :create_calls)}
      - **Save:** #{content.dig(:database_operations, :save_calls)}
      - **Find:** #{content.dig(:database_operations, :find_calls)}
      - **Where:** #{content.dig(:database_operations, :where_calls)}
      
      #### External Operations
      - **HTTP Requests:** #{content.dig(:external_calls, :http_requests)}
      - **File Operations:** #{content.dig(:external_calls, :file_operations)}
      - **System Calls:** #{content.dig(:external_calls, :system_calls)}
      
      #### Factory Usage
      - **Create:** #{content.dig(:factory_usage, :create_factory)}
      - **Build:** #{content.dig(:factory_usage, :build_factory)}
      
      ---
      
    MARKDOWN
  end

  def generate_summary_and_recommendations
    # Analyze patterns across all specs
    total_tests = @results.sum { |r| r[:data].dig(:content, :test_count) || 0 }
    avg_complexity = @results.sum { |r| r[:data].dig(:content, :setup_complexity, :complexity_score) || 0 } / @results.length.to_f
    
    <<~MARKDOWN
      ## Summary and Recommendations
      
      ### Key Findings
      
      **Total Tests Analyzed:** #{total_tests}
      **Average Setup Complexity:** #{avg_complexity.round(2)}
      
      Based on the analysis of the slowest specs, here are the main performance bottlenecks:
      
      1. **Test Setup Overhead**: High complexity scores indicate expensive test setup
      2. **Database Operations**: Multiple database calls per test
      3. **Rails Environment Loading**: Each spec file loads the full Rails environment
      
      ### Specific Recommendations
      
      1. **Optimize Test Setup**:
         - Reduce the number of `before` blocks
         - Use `let` instead of `let!` where possible for lazy loading
         - Share expensive setup across multiple tests using `shared_context`
      
      2. **Database Optimization**:
         - Use database transactions for faster cleanup
         - Implement database cleaner strategies
         - Consider using `build` instead of `create` where persistence isn't needed
      
      3. **Test Structure**:
         - Group related tests to share expensive setup
         - Consider splitting large test files with many contexts
         - Use `subject` to reduce duplication
      
      4. **Rails Configuration**:
         - Optimize test environment configuration
         - Consider using faster test databases
         - Reduce unnecessary middleware in test environment
      
      ### Performance Monitoring
      
      - Regularly run this analysis to track performance improvements
      - Set up CI checks for test performance regression
      - Consider implementing parallel test execution for the test suite
      
      ---
      
      **Analysis Data Location:** `#{ANALYSIS_DIR}/`
      **Generated by:** Enhanced Sentry Rails Spec Analyzer
    MARKDOWN
  end
end

# Run the analyzer
if __FILE__ == $0
  analyzer = EnhancedSpecAnalyzer.new
  analyzer.run
end
