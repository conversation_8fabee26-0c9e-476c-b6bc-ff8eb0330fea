# Sentry Rails - Slowest Specs Performance Analysis

**Generated:** 2025-08-22 21:15:59
**Tool:** Vernier Profiler
**Target:** Top 10 slowest specs in sentry-rails

## Executive Summary

This report analyzes the performance characteristics of the slowest RSpec tests in the sentry-rails gem using the Vernier profiler. Each test was profiled individually to identify performance bottlenecks and optimization opportunities.

## Individual Spec Analysis

### 1. spec/sentry/rails/log_subscribers/action_mailer_subscriber_spec.rb

**Total Time:** 0 seconds
**Total Samples:** 0
**Thread Count:** 21
**GC Events:** 663
**GC Time %:** 13477.87%

#### Top Methods by Sample Count

No method data available

#### Files with Most Activity

No file activity data available

---

### 2. spec/sentry/rails/log_subscribers/parameter_filter_spec.rb

**Total Time:** 0 seconds
**Total Samples:** 0
**Thread Count:** 2
**GC Events:** 409
**GC Time %:** 9038.3%

#### Top Methods by Sample Count

No method data available

#### Files with Most Activity

No file activity data available

---

### 3. spec/sentry/rails/log_subscribers/parameter_filter_spec.rb

**Total Time:** 0 seconds
**Total Samples:** 0
**Thread Count:** 2
**GC Events:** 408
**GC Time %:** 9098.17%

#### Top Methods by Sample Count

No method data available

#### Files with Most Activity

No file activity data available

---

### 4. spec/sentry/rails/log_subscribers/parameter_filter_spec.rb

**Total Time:** 0 seconds
**Total Samples:** 0
**Thread Count:** 2
**GC Events:** 411
**GC Time %:** 9672.94%

#### Top Methods by Sample Count

No method data available

#### Files with Most Activity

No file activity data available

---

### 5. spec/sentry/rails/log_subscribers/parameter_filter_spec.rb

**Total Time:** 0 seconds
**Total Samples:** 0
**Thread Count:** 2
**GC Events:** 415
**GC Time %:** 8920.31%

#### Top Methods by Sample Count

No method data available

#### Files with Most Activity

No file activity data available

---

### 6. spec/sentry/rails/log_subscribers/parameter_filter_spec.rb

**Total Time:** 0 seconds
**Total Samples:** 0
**Thread Count:** 2
**GC Events:** 408
**GC Time %:** 9145.32%

#### Top Methods by Sample Count

No method data available

#### Files with Most Activity

No file activity data available

---

### 7. spec/sentry/rails/action_cable_spec.rb

**Total Time:** 0 seconds
**Total Samples:** 0
**Thread Count:** 2
**GC Events:** 571
**GC Time %:** 10852.74%

#### Top Methods by Sample Count

No method data available

#### Files with Most Activity

No file activity data available

---

### 8. spec/sentry/rails/log_subscribers/active_job_subscriber_spec.rb

**Total Time:** 0 seconds
**Total Samples:** 0
**Thread Count:** 27
**GC Events:** 703
**GC Time %:** 14803.89%

#### Top Methods by Sample Count

No method data available

#### Files with Most Activity

No file activity data available

---

### 9. spec/sentry/rails/structured_logging_spec.rb

**Total Time:** 0 seconds
**Total Samples:** 0
**Thread Count:** 5
**GC Events:** 391
**GC Time %:** 7888.77%

#### Top Methods by Sample Count

No method data available

#### Files with Most Activity

No file activity data available

---

### 10. spec/sentry/rails/log_subscribers/parameter_filter_spec.rb

**Total Time:** 0 seconds
**Total Samples:** 0
**Thread Count:** 2
**GC Events:** 408
**GC Time %:** 9861.8%

#### Top Methods by Sample Count

No method data available

#### Files with Most Activity

No file activity data available

---

## Summary and Recommendations

### Key Findings

1. **Database Operations**: Many slow specs involve ActiveRecord operations
2. **Test Setup**: Significant time spent in test setup and teardown
3. **Rails Loading**: Framework initialization contributes to test slowness

### Optimization Recommendations

1. **Database Optimization**:
   - Use database transactions for faster test cleanup
   - Consider using factories more efficiently
   - Implement database cleaner strategies

2. **Test Structure**:
   - Group related tests to share expensive setup
   - Use `let` and `let!` appropriately for lazy loading
   - Consider splitting large test files

3. **Rails Configuration**:
   - Optimize test environment configuration
   - Consider using faster test databases (e.g., in-memory SQLite)
   - Reduce unnecessary middleware in test environment

### Next Steps

1. Review the individual spec analyses above
2. Focus optimization efforts on the highest-impact areas
3. Consider implementing parallel test execution
4. Regular performance monitoring of test suite

---

**Profile Data Location:** `spec_profiles/`
**Generated by:** Sentry Rails Spec Profiler
