#!/usr/bin/env ruby
# frozen_string_literal: true

# Sentry Rails Performance Analysis Runner
# This script runs both Vernier profiling and enhanced timing analysis

require 'fileutils'
require 'time'

class PerformanceAnalysisRunner
  def initialize
    @timestamp = Time.now.strftime('%Y%m%d_%H%M%S')
    @results_dir = "performance_analysis_#{@timestamp}"
  end

  def run
    puts "🚀 Starting Sentry Rails Performance Analysis"
    puts "📅 Timestamp: #{@timestamp}"
    puts "📁 Results will be saved to: #{@results_dir}"
    
    setup_results_directory
    
    puts "\n" + "="*80
    puts "🔬 Phase 1: Running Vernier Profiling Analysis"
    puts "="*80
    run_vernier_analysis
    
    puts "\n" + "="*80  
    puts "📊 Phase 2: Running Enhanced Timing Analysis"
    puts "="*80
    run_enhanced_analysis
    
    puts "\n" + "="*80
    puts "📋 Phase 3: Generating Summary Report"
    puts "="*80
    generate_summary_report
    
    puts "\n" + "="*80
    puts "✅ Analysis Complete!"
    puts "="*80
    puts "📁 Results location: #{@results_dir}/"
    puts "📄 Main report: #{@results_dir}/ANALYSIS_SUMMARY.md"
    puts "🔬 Vernier data: #{@results_dir}/vernier_profiles/"
    puts "📊 Timing data: #{@results_dir}/enhanced_analysis/"
  end

  private

  def setup_results_directory
    FileUtils.mkdir_p(@results_dir)
    puts "✅ Created results directory: #{@results_dir}"
  end

  def run_vernier_analysis
    puts "Running Vernier profiling script..."
    
    # Run the Vernier profiling script
    system("bundle exec ruby profile_slowest_specs.rb")
    
    # Move results to timestamped directory
    if Dir.exist?('spec_profiles')
      FileUtils.mv('spec_profiles', File.join(@results_dir, 'vernier_profiles'))
      puts "✅ Moved Vernier profiles to #{@results_dir}/vernier_profiles/"
    end
    
    if File.exist?('slowest_specs_analysis.md')
      FileUtils.mv('slowest_specs_analysis.md', File.join(@results_dir, 'vernier_analysis.md'))
      puts "✅ Moved Vernier report to #{@results_dir}/vernier_analysis.md"
    end
  end

  def run_enhanced_analysis
    puts "Running enhanced timing analysis script..."
    
    # Run the enhanced analysis script
    system("bundle exec ruby enhanced_spec_analysis.rb")
    
    # Move results to timestamped directory
    if Dir.exist?('enhanced_analysis')
      FileUtils.mv('enhanced_analysis', File.join(@results_dir, 'enhanced_analysis'))
      puts "✅ Moved enhanced analysis to #{@results_dir}/enhanced_analysis/"
    end
    
    if File.exist?('enhanced_slowest_specs_report.md')
      FileUtils.mv('enhanced_slowest_specs_report.md', File.join(@results_dir, 'enhanced_analysis_report.md'))
      puts "✅ Moved enhanced report to #{@results_dir}/enhanced_analysis_report.md"
    end
  end

  def generate_summary_report
    puts "Generating comprehensive summary report..."
    
    # Copy the summary template and update it with current data
    if File.exist?('PERFORMANCE_ANALYSIS_SUMMARY.md')
      summary_content = File.read('PERFORMANCE_ANALYSIS_SUMMARY.md')
      
      # Update timestamp in the summary
      summary_content.gsub!(/\*\*Generated:\*\* \d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/, 
                           "**Generated:** #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}")
      
      # Add analysis run information
      summary_content += "\n\n## Analysis Run Information\n\n"
      summary_content += "- **Run Timestamp:** #{@timestamp}\n"
      summary_content += "- **Vernier Profiles:** `#{@results_dir}/vernier_profiles/`\n"
      summary_content += "- **Enhanced Analysis:** `#{@results_dir}/enhanced_analysis/`\n"
      summary_content += "- **Vernier Report:** `#{@results_dir}/vernier_analysis.md`\n"
      summary_content += "- **Enhanced Report:** `#{@results_dir}/enhanced_analysis_report.md`\n"
      
      File.write(File.join(@results_dir, 'ANALYSIS_SUMMARY.md'), summary_content)
      puts "✅ Generated summary report: #{@results_dir}/ANALYSIS_SUMMARY.md"
    end
    
    # Create a quick reference file
    create_quick_reference
  end

  def create_quick_reference
    quick_ref = <<~MARKDOWN
      # Quick Reference - Performance Analysis #{@timestamp}

      ## Files Generated

      ### Main Reports
      - `ANALYSIS_SUMMARY.md` - Comprehensive analysis summary
      - `vernier_analysis.md` - Vernier profiler results
      - `enhanced_analysis_report.md` - Detailed timing analysis

      ### Data Directories
      - `vernier_profiles/` - Raw Vernier profiling data (JSON)
      - `enhanced_analysis/` - Detailed timing and content analysis (JSON)

      ## Key Findings Summary

      Run the analysis to see:
      - Slowest specs identified
      - Performance bottlenecks
      - GC activity analysis
      - Memory usage patterns
      - Optimization recommendations

      ## Next Steps

      1. Review `ANALYSIS_SUMMARY.md` for comprehensive findings
      2. Implement recommended optimizations
      3. Re-run analysis to measure improvements
      4. Set up regular performance monitoring

      ## Running Analysis Again

      ```bash
      bundle exec ruby run_performance_analysis.rb
      ```

      Generated: #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}
    MARKDOWN

    File.write(File.join(@results_dir, 'README.md'), quick_ref)
    puts "✅ Created quick reference: #{@results_dir}/README.md"
  end
end

# Run the analysis if this script is executed directly
if __FILE__ == $0
  runner = PerformanceAnalysisRunner.new
  runner.run
end
