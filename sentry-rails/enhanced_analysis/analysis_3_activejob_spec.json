{"spec_file": "spec/sentry/rails/activejob_spec.rb", "expected_time": 1.22, "timing": {"runs": [4.078458523988957, 3.905193156009773, 3.894870678996085], "average": 3.9595074529982717, "min": 3.894870678996085, "max": 4.078458523988957, "variance": 0.007092}, "content": {"total_lines": 433, "test_count": 22, "setup_complexity": {"before_blocks": 8, "let_blocks": 0, "subject_blocks": 0, "context_blocks": 13, "describe_blocks": 2, "complexity_score": 23}, "database_operations": {"create_calls": 5, "save_calls": 0, "find_calls": 1, "where_calls": 0, "update_calls": 0, "destroy_calls": 0}, "external_calls": {"http_requests": 0, "api_calls": 0, "file_operations": 0, "system_calls": 0}, "factory_usage": {"create_factory": 0, "build_factory": 0, "attributes_for": 0}, "let_statements": {"let_lazy": 0, "let_eager": 0}}, "timestamp": "2025-08-22T21:18:33+00:00"}