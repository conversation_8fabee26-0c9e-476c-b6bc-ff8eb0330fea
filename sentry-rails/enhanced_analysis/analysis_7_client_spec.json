{"spec_file": "spec/sentry/rails/client_spec.rb", "expected_time": 0.93564, "timing": {"runs": [4.171482938996633, 4.499178325000685, 4.622858372997143], "average": 4.431173212331487, "min": 4.171482938996633, "max": 4.622858372997143, "variance": 0.036269}, "content": {"total_lines": 69, "test_count": 2, "setup_complexity": {"before_blocks": 3, "let_blocks": 0, "subject_blocks": 0, "context_blocks": 2, "describe_blocks": 0, "complexity_score": 5}, "database_operations": {"create_calls": 0, "save_calls": 0, "find_calls": 0, "where_calls": 0, "update_calls": 0, "destroy_calls": 0}, "external_calls": {"http_requests": 0, "api_calls": 0, "file_operations": 0, "system_calls": 0}, "factory_usage": {"create_factory": 0, "build_factory": 0, "attributes_for": 0}, "let_statements": {"let_lazy": 0, "let_eager": 0}}, "timestamp": "2025-08-22T21:19:23+00:00"}