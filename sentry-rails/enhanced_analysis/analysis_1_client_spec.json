{"spec_file": "spec/sentry/rails/client_spec.rb", "expected_time": 1.67, "timing": {"runs": [4.5779273210064275, 4.345504497003276, 3.911807034004596], "average": 4.2784129506714335, "min": 3.911807034004596, "max": 4.5779273210064275, "variance": 0.076203}, "content": {"total_lines": 69, "test_count": 2, "setup_complexity": {"before_blocks": 3, "let_blocks": 0, "subject_blocks": 0, "context_blocks": 2, "describe_blocks": 0, "complexity_score": 5}, "database_operations": {"create_calls": 0, "save_calls": 0, "find_calls": 0, "where_calls": 0, "update_calls": 0, "destroy_calls": 0}, "external_calls": {"http_requests": 0, "api_calls": 0, "file_operations": 0, "system_calls": 0}, "factory_usage": {"create_factory": 0, "build_factory": 0, "attributes_for": 0}, "let_statements": {"let_lazy": 0, "let_eager": 0}}, "timestamp": "2025-08-22T21:18:09+00:00"}