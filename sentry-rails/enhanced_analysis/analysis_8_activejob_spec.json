{"spec_file": "spec/sentry/rails/activejob_spec.rb", "expected_time": 0.92457, "timing": {"runs": [4.456492280005477, 4.229050420995918, 4.0658664840011625], "average": 4.250469728334186, "min": 4.0658664840011625, "max": 4.456492280005477, "variance": 0.025661}, "content": {"total_lines": 433, "test_count": 22, "setup_complexity": {"before_blocks": 8, "let_blocks": 0, "subject_blocks": 0, "context_blocks": 13, "describe_blocks": 2, "complexity_score": 23}, "database_operations": {"create_calls": 5, "save_calls": 0, "find_calls": 1, "where_calls": 0, "update_calls": 0, "destroy_calls": 0}, "external_calls": {"http_requests": 0, "api_calls": 0, "file_operations": 0, "system_calls": 0}, "factory_usage": {"create_factory": 0, "build_factory": 0, "attributes_for": 0}, "let_statements": {"let_lazy": 0, "let_eager": 0}}, "timestamp": "2025-08-22T21:19:36+00:00"}